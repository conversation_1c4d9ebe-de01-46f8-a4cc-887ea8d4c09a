package products

import (
	"time"
)

// Product represents the product entity in the domain
type Product struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Price       float64   `json:"price"`
	Category    string    `json:"category"`
	Stock       int       `json:"stock"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ProductRepository defines the interface for product data operations
type ProductRepository interface {
	Create(product *Product) error
	Update(product *Product) error
	GetByID(id string) (*Product, error)
	Delete(id string) error
	List() ([]*Product, error)
}
